import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/loyalty/loyalty_program_model.dart';
import 'package:culture_connect/models/loyalty/loyalty_reward.dart';
import 'package:culture_connect/providers/loyalty_provider.dart';
import 'package:culture_connect/screens/loyalty/loyalty_reward_details_screen.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';

/// A screen that displays loyalty rewards
class LoyaltyRewardsScreen extends ConsumerStatefulWidget {
  /// The loyalty program
  final LoyaltyProgramModel loyaltyProgram;

  /// Creates a new loyalty rewards screen
  const LoyaltyRewardsScreen({
    super.key,
    required this.loyaltyProgram,
  });

  @override
  ConsumerState<LoyaltyRewardsScreen> createState() =>
      _LoyaltyRewardsScreenState();
}

class _LoyaltyRewardsScreenState extends ConsumerState<LoyaltyRewardsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final rewardsAsync = ref.watch(loyaltyRewardsProvider);

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Rewards',
        showBackButton: true,
      ),
      body: Column(
        children: [
          // Points summary
          Container(
            padding: const EdgeInsets.all(16),
            color: AppTheme.primaryColor.withAlpha(26),
            child: Row(
              children: [
                const Icon(
                  Icons.star,
                  size: 24,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Your Points Balance',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${widget.loyaltyProgram.pointsBalance}',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: widget.loyaltyProgram.tier.color.withAlpha(26),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: widget.loyaltyProgram.tier.color,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        widget.loyaltyProgram.tier.icon,
                        size: 16,
                        color: widget.loyaltyProgram.tier.color,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        widget.loyaltyProgram.tier.displayName,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: widget.loyaltyProgram.tier.color,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Tab bar
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'Available'),
              Tab(text: 'Redeemed'),
              Tab(text: 'All'),
            ],
            labelColor: AppTheme.primaryColor,
            unselectedLabelColor: AppTheme.textSecondaryColor,
            indicatorColor: AppTheme.primaryColor,
          ),

          // Tab content
          Expanded(
            child: rewardsAsync.when(
              data: (rewards) {
                // Filter rewards
                final availableRewards = rewards
                    .where((reward) =>
                        reward.status == LoyaltyRewardStatus.available)
                    .toList();
                final redeemedRewards = rewards
                    .where((reward) =>
                        reward.status == LoyaltyRewardStatus.redeemed)
                    .toList();

                return TabBarView(
                  controller: _tabController,
                  children: [
                    // Available rewards
                    _buildRewardsList(availableRewards),

                    // Redeemed rewards
                    _buildRewardsList(redeemedRewards),

                    // All rewards
                    _buildRewardsList(rewards),
                  ],
                );
              },
              loading: () => const Center(
                child: LoadingIndicator(),
              ),
              error: (error, stackTrace) => Center(
                child: ErrorView(
                  error: error.toString(),
                  onRetry: () => ref.refresh(loyaltyRewardsProvider),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRewardsList(List<LoyaltyReward> rewards) {
    if (rewards.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.card_giftcard,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            const Text(
              'No rewards found',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      );
    }

    // Sort rewards by points required (lowest first)
    final sortedRewards = List<LoyaltyReward>.from(rewards)
      ..sort((a, b) => a.pointsRequired.compareTo(b.pointsRequired));

    // Group rewards by type
    final groupedRewards = <LoyaltyRewardType, List<LoyaltyReward>>{};

    for (final reward in sortedRewards) {
      if (!groupedRewards.containsKey(reward.type)) {
        groupedRewards[reward.type] = [];
      }

      groupedRewards[reward.type]!.add(reward);
    }

    // Sort types by name
    final sortedTypes = groupedRewards.keys.toList()
      ..sort((a, b) => a.displayName.compareTo(b.displayName));

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: sortedTypes.length,
      itemBuilder: (context, index) {
        final type = sortedTypes[index];
        final typeRewards = groupedRewards[type]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Type header
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                children: [
                  Icon(
                    type.icon,
                    size: 20,
                    color: AppTheme.primaryColor,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    type.displayName,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ],
              ),
            ),

            // Type rewards
            ...typeRewards.map((reward) => _buildRewardItem(reward)),

            // Divider
            if (index < sortedTypes.length - 1) const Divider(height: 32),
          ],
        );
      },
    );
  }

  Widget _buildRewardItem(LoyaltyReward reward) {
    final isRedeemed = reward.status == LoyaltyRewardStatus.redeemed;
    final canRedeem = !isRedeemed &&
        widget.loyaltyProgram.pointsBalance >= reward.pointsRequired &&
        (reward.minimumTier == null ||
            widget.loyaltyProgram.tier.index >= reward.minimumTier!.index);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => LoyaltyRewardDetailsScreen(
                reward: reward,
                loyaltyProgram: widget.loyaltyProgram,
              ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Reward name and status
              Row(
                children: [
                  Expanded(
                    child: Text(
                      reward.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                  ),
                  if (isRedeemed)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.green.withAlpha(26),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.green,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.check_circle,
                            size: 14,
                            color: Colors.green,
                          ),
                          const SizedBox(width: 4),
                          const Text(
                            'Redeemed',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 8),

              // Reward description
              Text(
                reward.description,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondaryColor,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 16),

              // Points required and value
              Row(
                children: [
                  // Points required
                  Row(
                    children: [
                      const Icon(
                        Icons.star,
                        size: 16,
                        color: AppTheme.primaryColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${reward.pointsRequired} points',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(width: 16),

                  // Value
                  if (reward.formattedValue != null) ...[
                    const Icon(
                      Icons.monetization_on,
                      size: 16,
                      color: Colors.amber,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Value: ${reward.formattedValue}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 16),

              // Redeem button or redemption details
              if (isRedeemed) ...[
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 16,
                        color: AppTheme.textSecondaryColor,
                      ),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Tap to view redemption details',
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 12,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ],
                  ),
                ),
              ] else ...[
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: canRedeem ? () => _redeemReward(reward) : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      canRedeem ? 'Redeem Reward' : 'Not Enough Points',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _redeemReward(LoyaltyReward reward) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Redeem Reward'),
        content: Text(
          'Are you sure you want to redeem ${reward.name} for ${reward.pointsRequired} points?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Redeem'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // Show loading indicator
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Processing redemption...'),
            duration: Duration(seconds: 1),
          ),
        );

        // Redeem the reward
        await ref
            .read(redeemLoyaltyRewardNotifierProvider.notifier)
            .redeemReward(reward.id);

        if (context.mounted) {
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Successfully redeemed ${reward.name}'),
              backgroundColor: Colors.green,
            ),
          );

          // Navigate to reward details screen
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => LoyaltyRewardDetailsScreen(
                reward: reward.copyWith(
                  status: LoyaltyRewardStatus.redeemed,
                  redemptionDate: DateTime.now(),
                ),
                loyaltyProgram: widget.loyaltyProgram.copyWith(
                  pointsBalance: widget.loyaltyProgram.pointsBalance -
                      reward.pointsRequired,
                ),
              ),
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error redeeming reward: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        // Reset the notifier
        ref.read(redeemLoyaltyRewardNotifierProvider.notifier).reset();
      }
    }
  }
}
