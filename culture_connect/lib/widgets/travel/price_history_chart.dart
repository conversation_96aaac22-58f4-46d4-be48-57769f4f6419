import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/travel/price_history_model.dart';
import 'package:culture_connect/providers/price_comparison_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget that displays a price history chart
class PriceHistory<PERSON>hart extends ConsumerStatefulWidget {
  /// The travel service type
  final String serviceType;

  /// The travel service ID
  final String serviceId;

  /// The travel service name
  final String serviceName;

  /// Whether to show the forecast
  final bool showForecast;

  /// Whether to show the trend
  final bool showTrend;

  /// Whether to show the refresh button
  final bool showRefreshButton;

  /// Creates a new price history chart
  const PriceHistoryChart({
    super.key,
    required this.serviceType,
    required this.serviceId,
    required this.serviceName,
    this.showForecast = true,
    this.showTrend = true,
    this.showRefreshButton = true,
  });

  @override
  ConsumerState<PriceHistoryChart> createState() => _PriceHistoryChartState();
}

class _PriceHistoryChartState extends ConsumerState<PriceHistoryChart> {
  String _selectedSourceId = '';
  bool _showAllSources = true;

  @override
  Widget build(BuildContext context) {
    final serviceEntry = MapEntry(widget.serviceType, widget.serviceId);
    final priceHistoryAsync = ref.watch(priceHistoryProvider(serviceEntry));
    final priceTrendAsync = ref.watch(priceTrendProvider(serviceEntry));
    final priceForecastAsync = ref.watch(priceForecastProvider(serviceEntry));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Price History',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      widget.serviceName,
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondaryColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              if (widget.showRefreshButton)
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () {
                    ref.invalidate(priceHistoryProvider(serviceEntry));
                  },
                  tooltip: 'Refresh history',
                ),
            ],
          ),
        ),

        // Price trend and forecast
        if (widget.showTrend || widget.showForecast)
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                if (widget.showTrend)
                  Expanded(
                    child: priceTrendAsync.when(
                      data: (trend) {
                        if (trend == null) return const SizedBox.shrink();

                        final isPositive = trend > 0;
                        final trendColor =
                            isPositive ? Colors.red : Colors.green;
                        final trendIcon = isPositive
                            ? Icons.trending_up
                            : Icons.trending_down;

                        return Container(
                          padding: EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: trendColor.withAlpha(25),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                trendIcon,
                                size: 16,
                                color: trendColor,
                              ),
                              SizedBox(width: 4),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      '30-Day Trend',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: AppTheme.textSecondaryColor,
                                      ),
                                    ),
                                    Text(
                                      '${isPositive ? '+' : ''}${trend.toStringAsFixed(1)}%',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                        color: trendColor,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                      loading: () => const SizedBox.shrink(),
                      error: (_, __) => const SizedBox.shrink(),
                    ),
                  ),
                if (widget.showTrend && widget.showForecast) SizedBox(width: 8),
                if (widget.showForecast)
                  Expanded(
                    child: priceForecastAsync.when(
                      data: (forecast) {
                        if (forecast == null) return const SizedBox.shrink();

                        return Container(
                          padding: EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.blue.withAlpha(25),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.timeline,
                                size: 16,
                                color: Colors.blue,
                              ),
                              SizedBox(width: 4),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      '30-Day Forecast',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: AppTheme.textSecondaryColor,
                                      ),
                                    ),
                                    Text(
                                      '${widget.serviceType == 'flight' ? 'USD' : '\$'}${forecast.toStringAsFixed(2)}',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.blue,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                      loading: () => const SizedBox.shrink(),
                      error: (_, __) => const SizedBox.shrink(),
                    ),
                  ),
              ],
            ),
          ),

        SizedBox(height: 16),

        // Price history chart
        priceHistoryAsync.when(
          data: (history) {
            if (history.entries.isEmpty) {
              return Center(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Icon(
                        Icons.timeline_outlined,
                        size: 48,
                        color: Colors.grey[400],
                      ),
                      SizedBox(height: 16),
                      const Text(
                        'No price history available',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (widget.showRefreshButton)
                        ElevatedButton.icon(
                          onPressed: () {
                            ref.invalidate(priceHistoryProvider(serviceEntry));
                          },
                          icon: const Icon(Icons.refresh),
                          label: const Text('Refresh'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            foregroundColor: Colors.white,
                          ),
                        ),
                    ],
                  ),
                ),
              );
            }

            // Get unique sources
            final sources = <String>{};
            for (final entry in history.entries) {
              sources.add(entry.source.id);
            }

            // If no source is selected, select all
            if (_selectedSourceId.isEmpty && !_showAllSources) {
              setState(() {
                _showAllSources = true;
              });
            }

            // Filter entries by selected source
            final filteredEntries = _showAllSources
                ? history.entries
                : history.entries
                    .where((e) => e.source.id == _selectedSourceId)
                    .toList();

            if (filteredEntries.isEmpty) {
              setState(() {
                _showAllSources = true;
              });
              return const SizedBox.shrink();
            }

            return Column(
              children: [
                // Source filter
                if (sources.length > 1)
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Row(
                      children: [
                        const Text(
                          'Source:',
                          style: TextStyle(
                            fontSize: 14,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              children: [
                                // All sources option
                                _buildSourceFilterChip(
                                  'All Sources',
                                  isSelected: _showAllSources,
                                  onSelected: (selected) {
                                    setState(() {
                                      _showAllSources = selected;
                                      if (selected) {
                                        _selectedSourceId = '';
                                      }
                                    });
                                  },
                                ),
                                const SizedBox(width: 8),
                                // Individual source options
                                ...sources.map((sourceId) {
                                  final sourceName = history.entries
                                      .firstWhere(
                                          (e) => e.source.id == sourceId)
                                      .source
                                      .name;

                                  return Padding(
                                    padding: const EdgeInsets.only(right: 8),
                                    child: _buildSourceFilterChip(
                                      sourceName,
                                      isSelected: !_showAllSources &&
                                          _selectedSourceId == sourceId,
                                      onSelected: (selected) {
                                        setState(() {
                                          _showAllSources = !selected;
                                          _selectedSourceId =
                                              selected ? sourceId : '';
                                        });
                                      },
                                    ),
                                  );
                                }),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                const SizedBox(height: 16),

                // Chart
                SizedBox(
                  height: 200,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: _buildPriceHistoryChart(
                        filteredEntries, history.priceForecast),
                  ),
                ),
              ],
            );
          },
          loading: () => const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: Column(
                children: [
                  CircularProgressIndicator(
                    valueColor:
                        AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Loading price history...',
                    style: TextStyle(
                      fontSize: 16,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
          error: (error, stackTrace) => Center(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 48,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Error loading price history',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    error.toString(),
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  if (widget.showRefreshButton)
                    ElevatedButton.icon(
                      onPressed: () {
                        ref.invalidate(priceHistoryProvider(serviceEntry));
                      },
                      icon: const Icon(Icons.refresh),
                      label: const Text('Try Again'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSourceFilterChip(
    String label, {
    required bool isSelected,
    required Function(bool) onSelected,
  }) {
    return FilterChip(
      label: Text(
        label,
        style: TextStyle(
          fontSize: 12,
          color: isSelected ? Colors.white : AppTheme.textSecondaryColor,
        ),
      ),
      selected: isSelected,
      onSelected: onSelected,
      backgroundColor: Colors.grey[200],
      selectedColor: AppTheme.primaryColor,
      checkmarkColor: Colors.white,
    );
  }

  Widget _buildPriceHistoryChart(
      List<PriceHistoryEntry> entries, double? forecast) {
    // Sort entries by date
    final sortedEntries = List<PriceHistoryEntry>.from(entries)
      ..sort((a, b) => a.date.compareTo(b.date));

    // Group entries by date and source
    final entriesByDateAndSource = <String, Map<String, PriceHistoryEntry>>{};
    for (final entry in sortedEntries) {
      final dateKey = DateFormat('yyyy-MM-dd').format(entry.date);
      entriesByDateAndSource[dateKey] = entriesByDateAndSource[dateKey] ?? {};
      entriesByDateAndSource[dateKey]![entry.source.id] = entry;
    }

    // Get unique dates and sources
    final dates = entriesByDateAndSource.keys.toList()..sort();
    final sources = <String>{};
    for (final entry in sortedEntries) {
      sources.add(entry.source.id);
    }

    // Assign colors to sources
    final sourceColors = <String, Color>{};
    final colors = [
      Colors.blue,
      Colors.red,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.pink,
      Colors.amber,
    ];

    int colorIndex = 0;
    for (final source in sources) {
      sourceColors[source] = colors[colorIndex % colors.length];
      colorIndex++;
    }

    // Find min and max prices
    double minPrice = double.infinity;
    double maxPrice = 0;

    for (final entry in sortedEntries) {
      if (entry.price < minPrice) {
        minPrice = entry.price;
      }
      if (entry.price > maxPrice) {
        maxPrice = entry.price;
      }
    }

    // Add some padding to min and max
    minPrice = minPrice * 0.9;
    maxPrice = maxPrice * 1.1;

    // Include forecast in min/max if available
    if (forecast != null && widget.showForecast) {
      if (forecast < minPrice) {
        minPrice = forecast * 0.9;
      }
      if (forecast > maxPrice) {
        maxPrice = forecast * 1.1;
      }
    }

    // Create line chart data
    final lineBarsData = <LineChartBarData>[];

    // Add lines for each source
    if (_showAllSources) {
      for (final source in sources) {
        final spots = <FlSpot>[];

        for (int i = 0; i < dates.length; i++) {
          final dateKey = dates[i];
          final sourceEntries = entriesByDateAndSource[dateKey];

          if (sourceEntries != null && sourceEntries.containsKey(source)) {
            final entry = sourceEntries[source]!;
            spots.add(FlSpot(i.toDouble(), entry.price));
          }
        }

        if (spots.isNotEmpty) {
          lineBarsData.add(
            LineChartBarData(
              spots: spots,
              isCurved: true,
              color: sourceColors[source],
              barWidth: 3,
              isStrokeCapRound: true,
              dotData: FlDotData(show: spots.length < 10),
              belowBarData: BarAreaData(
                show: true,
                color: sourceColors[source]!.withAlpha(25),
              ),
            ),
          );
        }
      }
    } else {
      // Add line for selected source
      final spots = <FlSpot>[];

      for (int i = 0; i < dates.length; i++) {
        final dateKey = dates[i];
        final sourceEntries = entriesByDateAndSource[dateKey];

        if (sourceEntries != null &&
            sourceEntries.containsKey(_selectedSourceId)) {
          final entry = sourceEntries[_selectedSourceId]!;
          spots.add(FlSpot(i.toDouble(), entry.price));
        }
      }

      if (spots.isNotEmpty) {
        lineBarsData.add(
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: sourceColors[_selectedSourceId],
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(show: spots.length < 10),
            belowBarData: BarAreaData(
              show: true,
              color: sourceColors[_selectedSourceId]!.withAlpha(25),
            ),
          ),
        );
      }
    }

    // Add forecast line if available
    if (forecast != null && widget.showForecast && dates.isNotEmpty) {
      final lastDateIndex = dates.length - 1;
      final forecastDateIndex = lastDateIndex + 30; // 30 days in the future

      lineBarsData.add(
        LineChartBarData(
          spots: [
            FlSpot(lastDateIndex.toDouble(), sortedEntries.last.price),
            FlSpot(forecastDateIndex.toDouble(), forecast),
          ],
          isCurved: false,
          color: Colors.blue.withAlpha(128),
          barWidth: 2,
          isStrokeCapRound: true,
          dotData: const FlDotData(show: false),
          dashArray: [5, 5], // Dashed line
        ),
      );
    }

    return LineChart(
      LineChartData(
        lineBarsData: lineBarsData,
        minY: minPrice,
        maxY: maxPrice,
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                return Text(
                  '${widget.serviceType == 'flight' ? 'USD' : '\$'}${value.toInt()}',
                  style: const TextStyle(
                    color: AppTheme.textSecondaryColor,
                    fontSize: 10,
                  ),
                );
              },
              reservedSize: 40,
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index < 0 || index >= dates.length) {
                  return const SizedBox.shrink();
                }

                final dateKey = dates[index];
                final date = DateTime.parse(dateKey);

                return Text(
                  DateFormat('MM/dd').format(date),
                  style: const TextStyle(
                    color: AppTheme.textSecondaryColor,
                    fontSize: 10,
                  ),
                );
              },
              reservedSize: 30,
            ),
          ),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: (maxPrice - minPrice) / 5,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey[300],
              strokeWidth: 1,
            );
          },
        ),
        borderData: FlBorderData(
          show: true,
          border: Border(
            bottom: BorderSide(color: Colors.grey[400]!, width: 1),
            left: BorderSide(color: Colors.grey[400]!, width: 1),
          ),
        ),
      ),
    );
  }
}
